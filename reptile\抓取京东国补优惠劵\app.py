import time
from urllib.parse import urlencode

import requests


def nationalSubsidy_coupons(cookies, h5st, x_api_eid_token):
    url = 'https://api.m.jd.com/api?fid=bindingQualification'
    t = int(time.time()*1000)
    # cookies = 'shshshfpa=07304dab-55ac-a21b-91e7-5a3b02f48ca8-1720075793; shshshfpx=07304dab-55ac-a21b-91e7-5a3b02f48ca8-1720075793; b_dw=400; b_dpr=3; b_webp=1; b_avif=1; jcap_dvzw_fp=t4xiUQ2zn_wNsrPOISL03Cq-Du8vwzS6dc4Aes7qj4AoN4LxRFkMyXM_tD-83m2xRSXJR8B8hchC3yIwv8fnFQ==; webp=1; visitkey=7276259595972947487; qid_uid=99741345-668b-4a7e-82ef-************; qid_fs=1730421376146; qd_uid=M2Y9UY0U-ZJABIBNBRV6JS9H5FFBV; qd_fs=1730437585075; qd_ls=1730437585075; qd_ts=1730437585075; qd_sq=1; 3AB9D23F7A4B3C9B=OVCE4YXH2RPNFUS4T4CU5S7UGBXZ5LQMAISF4RQTQGR4A7LJIDF73VLXIWBMQQH5HPLGYOXQMM4C3VRYPK6NKQHCF4; unionwsws=%7B%22devicefinger%22%3A%22eidAfd0e8122a4s8x3wanGTDTEqxUPGVb6hU9Md8HCzNpisD2CkE7jGfnZUpe2I2eR%2FqBUtYBkUpjmllxDbZ8W2eZmorUrCay5YQipP%2FSi3coC%2BEv51k%22%7D; __wga=1738902465497.1738902465497.1736760986088.1730117295515.1.6; source=sourceYX1; sid=; pt_pin=jd_4bc96664cc53d; pwdt_id=jd_4bc96664cc53d; __jdc=180962401; cid=8; language=zh_CN; deviceid_pdj_jd=5ed7a959829b73a6; unpl=JF8EAK9nNSttXElWAE9XTEAZHg9UW15bTUQBbzBSXV8LHlQHH1JMQRd7XlVdWRRKEB9vbhRVVFNOXQ4YBCsiE0pfUVZbAUMWBF9XAlRUFQoGSCsBGyIRe11SXloOQxMBamMFXFtYTFcNHwYcEBJJbVRYbQ9LHjNfVwBUXFhIXA0YARIiEXtfVF9dCUMeA2puNR8zWQZUAxsFHRoUSVhQXlUOSxAAZ2MBU19aSWQHGwEbFSBI%7CJF8EAL1nNSttXEhUUhxXGUcQT1VTWw4NQh4BbDNSUVhaG1EHSQEeQBR7XlVdWBRLFB9sZRRVVFNJVg4eAysiE0pfV1ZZDU8RA19XBVFfNkxXAx8EHBAZTzNUXjNvE0JXMQkAOl5YTlQFGgEYIhNLbVVuXQ5LHwVqYAJXXltLUwUaARwTGUldXF5tCXseAl9nBFVdW0NVBRwLHhAge1xkX20Je1xtbioFUl1QTVECHAEYERBMXVVdWglCFQNnZzVVbVg; pt_key=app_openAAJoXsQbADA6du9wrnrTY7gXnF2sencpUQiK6gV4lR93G1tgjrDXp1aReSv6JyWG1fhKd5gu9Vw; jdt_mp_gray=; qid_ls=1751073715063; qid_ts=1751092047366; qid_vis=15; qid_evord=2094; wxa_level=1; ipLoc-djd=22_1930_50947_0; bf_hybrid=e_MPlusNew_Home; p-request-id=jd_4bc96664cc53d2025062817UqV8Gvedg6; shshshfpv=JD022145b9ltM307e8AS175110350091907rgibGlCwLa8oQRLz6UGU5jCNZaN0VG5UHFWnWLcxvQS7coWeAVkvj7VlvDnjPBdpivXdSrxvcnUO9qJ25rHhfg1ley7ch~BApXSEW_utvJC9q4Q8kfutLLJaXe7mGZzSMDadIlX9xJ1ItZfQtLUkUO92n2uMIUmILEAlkCBNuJVR4w; joyya=1751103533.1751103572.53.0vkl9rq; __jdv=180962401%7Ckong%7Ct_1000170135%7Cxm%7C186_685fce8b7e843f0120666e90%40%26bv0io%2FWnAQjJFOW%2FsP5%2BR4Ty7Fdy0IabQdNLlH6dgvieTn5tOmns3XajAIzSc6Ip1751109271782%7C1751109271000; b_dh=687; shshshfpb=BApXSmjqDtfJAxuO7p-tMryMuM0y5Mj1yBmJGULxu9xJ1ItZfQtLUkUO92n2uMIUmILEpkEeBNuJVR4w; __jda=180962401.17197466222161026083191.1719746622.1751114220.1751120251.315; __jd_ref_cls=Productdetail_gbzgclick; mba_sid=630.3; pre_session=tdJUIQIONx0XH/ad5jI/9QEM1+c7cdE6|1333; pre_seq=1; __jdb=180962401.4.17197466222161026083191|315.1751120251; mba_muid=17197466222161026083191.630.1751120755388; 3AB9D23F7A4B3CSS=jdd03OVCE4YXH2RPNFUS4T4CU5S7UGBXZ5LQMAISF4RQTQGR4A7LJIDF73VLXIWBMQQH5HPLGYOXQMM4C3VRYPK6NKQHCF4AAAAMXW3XD44QAAAAAC2FBJ2WS2HV7GMX; sdtoken=AAbEsBpEIOVjqTAKCQtvQu17Rk4Fsh9U7QVAt1dDzh7girnmni0E0mbBN2XIjpARBfRjk-FqpmPgTNBx4Zo0wnfn5FFS-WDAFTasVnPq__FnWYEM8vJsXfe3doNPqV5IBT8; govLocation=%7B%22code%22%3A0%2C%22message%22%3A%22OK%22%2C%22region%22%3A%22%E4%B8%AD%E5%9B%BD%22%2C%22regionid%22%3A%220%22%2C%22province%22%3A%22%E5%9B%9B%E5%B7%9D%22%2C%22provinceid%22%3A%2222%22%2C%22city%22%3A%22%E6%88%90%E9%83%BD%E5%B8%82%22%2C%22cityid%22%3A%221930%22%2C%22district%22%3A%22%E6%AD%A6%E4%BE%AF%E5%8C%BA%22%2C%22districtid%22%3A%2250947%22%2C%22town%22%3A%22%E7%BA%A2%E7%89%8C%E6%A5%BC%E8%A1%97%E9%81%93%22%2C%22townid%22%3A%2257078%22%2C%22detailaddr%22%3A%22%22%2C%22fullAddress%22%3A%22%E5%9B%9B%E5%B7%9D%E6%88%90%E9%83%BD%E5%B8%82%E6%AD%A6%E4%BE%AF%E5%8C%BA%E7%BA%A2%E7%89%8C%E6%A5%BC%E8%A1%97%E9%81%93%22%2C%22oversea%22%3A%220%22%2C%22callType%22%3A%22GisService%22%2C%22srclng%22%3A104.02828%2C%22srclat%22%3A30.636166%2C%22updateTime%22%3A1751120895043%2C%22encryptLng%22%3A%22DGivq9j502W-BSi5c08mJw%22%2C%22encryptLat%22%3A%22NwNfwbsemQDhmnxt8DUjKA%22%2C%22gridId%22%3A0%2C%22poi%22%3A%22%22%2C%22accuracy%22%3A11%7D'
    headers = {
        "Cookie": cookies,
        "Content-Type": "application/x-www-form-urlencoded",
        "x-referer-page": "https://gov-subsidy.jd.com/pages/details/index",
        "Referer": "https://gov-subsidy.jd.com/",
        "User-Agent": "jdapp;android;15.1.55;;;M/5.0;appBuild/100987;ef/1;ep/%7B%22hdid%22%3A%22JM9F1ywUPwflvMIpYPok0tt5k9kW4ArJEU3lfLhxBqw%3D%22%2C%22ts%22%3A1751120251017%2C%22ridx%22%3A-1%2C%22cipher%22%3A%7B%22sv%22%3A%22CJU%3D%22%2C%22ad%22%3A%22DWVuD2O5DJu4CtvsDzDrDq%3D%3D%22%2C%22od%22%3A%22YJVrENq0ZJTtCzPvCWS3Cq%3D%3D%22%2C%22ov%22%3A%22CzU%3D%22%2C%22ud%22%3A%22DWVuD2O5DJu4CtvsDzDrDq%3D%3D%22%7D%2C%22ciphertype%22%3A5%2C%22version%22%3A%221.2.1%22%2C%22appname%22%3A%22com.jingdong.app.mall%22%7D;jdSupportDarkMode/0;Mozilla/5.0 (Linux; Android 15; 23127PN0CC Build/AQ3A.240627.003; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/137.0.7151.90 Mobile Safari/537.36"
    }
    dataJson = {
        "appid": "gov-subsidy-h5",
        "body": '{"provinceId":22,"cateId":"A04","cateName":"空调","clientVersion":"15.1.55","locProvinceId":22,"loCityId":1930,"otherPos":"{\"code\":0,\"message\":\"OK\",\"region\":\"中国\",\"regionid\":\"0\",\"province\":\"四川\",\"provinceid\":\"22\",\"city\":\"成都市\",\"cityid\":\"1930\",\"district\":\"武侯区\",\"districtid\":\"50947\",\"town\":\"红牌楼街道\",\"townid\":\"57078\",\"detailaddr\":\"\",\"fullAddress\":\"四川成都市武侯区红牌楼街道\",\"oversea\":\"0\",\"callType\":\"GisService\",\"srclng\":104.02828,\"srclat\":30.636166,\"updateTime\":1751120233505,\"encryptLng\":\"DGivq9j502W-BSi5c08mJw\",\"encryptLat\":\"NwNfwbsemQDhmnxt8DUjKA\",\"gridId\":0,\"poi\":\"\",\"accuracy\":11}","closeTime":"23:00","startTime":"8:00","receiveStatus":true,"channelId":"2025_22_1930_8","sourceChannelId":9}',
        "channelId": "2025_22_1930_8",
        "functionId": "bindingQualification",
        "h5st": str(h5st),
        "loginType": "null",
        "loginWQBiz": "",
        "t": t,
        "x-api-eid-token": str(x_api_eid_token)
    }
    # 1751122620
    # 1751121500928
    # print(requests.session().post(url=url, headers=headers, data=urlencode(dataJson, doseq=True), verify=False).json())

    print(requests.session().post(url=url, headers=headers, data=dataJson, verify=False).json())

if __name__ == '__main__':
    cookies = 'shshshfpa=07304dab-55ac-a21b-91e7-5a3b02f48ca8-1720075793; shshshfpx=07304dab-55ac-a21b-91e7-5a3b02f48ca8-1720075793; b_dw=400; b_dpr=3; b_webp=1; b_avif=1; jcap_dvzw_fp=t4xiUQ2zn_wNsrPOISL03Cq-Du8vwzS6dc4Aes7qj4AoN4LxRFkMyXM_tD-83m2xRSXJR8B8hchC3yIwv8fnFQ==; webp=1; visitkey=7276259595972947487; qid_uid=99741345-668b-4a7e-82ef-************; qid_fs=1730421376146; qd_uid=M2Y9UY0U-ZJABIBNBRV6JS9H5FFBV; qd_fs=1730437585075; qd_ls=1730437585075; qd_ts=1730437585075; qd_sq=1; 3AB9D23F7A4B3C9B=OVCE4YXH2RPNFUS4T4CU5S7UGBXZ5LQMAISF4RQTQGR4A7LJIDF73VLXIWBMQQH5HPLGYOXQMM4C3VRYPK6NKQHCF4; unionwsws=%7B%22devicefinger%22%3A%22eidAfd0e8122a4s8x3wanGTDTEqxUPGVb6hU9Md8HCzNpisD2CkE7jGfnZUpe2I2eR%2FqBUtYBkUpjmllxDbZ8W2eZmorUrCay5YQipP%2FSi3coC%2BEv51k%22%7D; __wga=1738902465497.1738902465497.1736760986088.1730117295515.1.6; source=sourceYX1; sid=; pt_pin=jd_4bc96664cc53d; pwdt_id=jd_4bc96664cc53d; __jdc=180962401; cid=8; language=zh_CN; deviceid_pdj_jd=5ed7a959829b73a6; unpl=JF8EAK9nNSttXElWAE9XTEAZHg9UW15bTUQBbzBSXV8LHlQHH1JMQRd7XlVdWRRKEB9vbhRVVFNOXQ4YBCsiE0pfUVZbAUMWBF9XAlRUFQoGSCsBGyIRe11SXloOQxMBamMFXFtYTFcNHwYcEBJJbVRYbQ9LHjNfVwBUXFhIXA0YARIiEXtfVF9dCUMeA2puNR8zWQZUAxsFHRoUSVhQXlUOSxAAZ2MBU19aSWQHGwEbFSBI%7CJF8EAL1nNSttXEhUUhxXGUcQT1VTWw4NQh4BbDNSUVhaG1EHSQEeQBR7XlVdWBRLFB9sZRRVVFNJVg4eAysiE0pfV1ZZDU8RA19XBVFfNkxXAx8EHBAZTzNUXjNvE0JXMQkAOl5YTlQFGgEYIhNLbVVuXQ5LHwVqYAJXXltLUwUaARwTGUldXF5tCXseAl9nBFVdW0NVBRwLHhAge1xkX20Je1xtbioFUl1QTVECHAEYERBMXVVdWglCFQNnZzVVbVg; pt_key=app_openAAJoXsQbADA6du9wrnrTY7gXnF2sencpUQiK6gV4lR93G1tgjrDXp1aReSv6JyWG1fhKd5gu9Vw; jdt_mp_gray=; qid_ls=1751073715063; qid_ts=1751092047366; qid_vis=15; qid_evord=2094; wxa_level=1; ipLoc-djd=22_1930_50947_0; bf_hybrid=e_MPlusNew_Home; p-request-id=jd_4bc96664cc53d2025062817UqV8Gvedg6; shshshfpv=JD022145b9ltM307e8AS175110350091907rgibGlCwLa8oQRLz6UGU5jCNZaN0VG5UHFWnWLcxvQS7coWeAVkvj7VlvDnjPBdpivXdSrxvcnUO9qJ25rHhfg1ley7ch~BApXSEW_utvJC9q4Q8kfutLLJaXe7mGZzSMDadIlX9xJ1ItZfQtLUkUO92n2uMIUmILEAlkCBNuJVR4w; joyya=1751103533.1751103572.53.0vkl9rq; __jdv=180962401%7Ckong%7Ct_1000170135%7Cxm%7C186_685fce8b7e843f0120666e90%40%26bv0io%2FWnAQjJFOW%2FsP5%2BR4Ty7Fdy0IabQdNLlH6dgvieTn5tOmns3XajAIzSc6Ip1751109271782%7C1751109271000; b_dh=687; shshshfpb=BApXSmjqDtfJAxuO7p-tMryMuM0y5Mj1yBmJGULxu9xJ1ItZfQtLUkUO92n2uMIUmILEpkEeBNuJVR4w; __jda=180962401.17197466222161026083191.1719746622.1751114220.1751120251.315; __jd_ref_cls=Productdetail_gbzgclick; mba_sid=630.18; pre_session=Yac1udhVS+BnLvULYOCIJAHcDpa0XFpO|1337; pre_seq=3; __jdb=180962401.18.17197466222161026083191|315.1751120251; mba_muid=17197466222161026083191.630.1751123900455; 3AB9D23F7A4B3CSS=jdd03OVCE4YXH2RPNFUS4T4CU5S7UGBXZ5LQMAISF4RQTQGR4A7LJIDF73VLXIWBMQQH5HPLGYOXQMM4C3VRYPK6NKQHCF4AAAAMXW4PDX4AAAAAADMND4KX3WVEQJQX; _gia_d=1; govLocation=%7B%22code%22%3A0%2C%22message%22%3A%22OK%22%2C%22region%22%3A%22%E4%B8%AD%E5%9B%BD%22%2C%22regionid%22%3A%220%22%2C%22province%22%3A%22%E5%9B%9B%E5%B7%9D%22%2C%22provinceid%22%3A%2222%22%2C%22city%22%3A%22%E6%88%90%E9%83%BD%E5%B8%82%22%2C%22cityid%22%3A%221930%22%2C%22district%22%3A%22%E6%AD%A6%E4%BE%AF%E5%8C%BA%22%2C%22districtid%22%3A%2250947%22%2C%22town%22%3A%22%E7%BA%A2%E7%89%8C%E6%A5%BC%E8%A1%97%E9%81%93%22%2C%22townid%22%3A%2257078%22%2C%22detailaddr%22%3A%22%22%2C%22fullAddress%22%3A%22%E5%9B%9B%E5%B7%9D%E6%88%90%E9%83%BD%E5%B8%82%E6%AD%A6%E4%BE%AF%E5%8C%BA%E7%BA%A2%E7%89%8C%E6%A5%BC%E8%A1%97%E9%81%93%22%2C%22oversea%22%3A%220%22%2C%22callType%22%3A%22GisService%22%2C%22srclng%22%3A104.02828%2C%22srclat%22%3A30.636166%2C%22updateTime%22%3A1751123902459%2C%22encryptLng%22%3A%22DGivq9j502W-BSi5c08mJw%22%2C%22encryptLat%22%3A%22NwNfwbsemQDhmnxt8DUjKA%22%2C%22gridId%22%3A0%2C%22poi%22%3A%22%22%2C%22accuracy%22%3A11%7D; sdtoken=AAbEsBpEIOVjqTAKCQtvQu174g9RV73HddqIxrN4K3dT-M8dvY83ZdeelOF8Sz97y7BdMTX8hr6GXiIXPYjhKBkrihyQ3YIuPSdgXiPQExdPy7LqBsQ7XMTDHA-n-auFULlSik_pv6GuB8Oo_F8'
    h5st = '20250628231943012;xdg3izwaz0h22305;1365e;tk03w95811cb118n2WTQtAv1hc10ytrG_eLaI59ERKxPL2mh69E3NlNmDH2oX3_qUStYVoZXifrtqBjwFz4mtpyqo9Gh;e2f488cacb0cdb5437823d0ba944090a4ade77287e70dfadead120e4861d2157;5.1;1751123982012;ri_uxFOm5u7i8mrU_GXW3FXU7SoV2lsm0msSIlsmOGuj2uMgM24WLlsmOGujMqLi7uLW7a4i2i4i4abiKZ7WJdIi3e7h4qbi_ubV6mogMuMgMuHdCRIWJRHmOuMsCmsiIVLi6arV3ebVKhLWLd4WNl4i7OLW1ibhKVLhMVIiLlsm0m8SNVHTNhImOuMsCurm0msh5lImOuMsCmMhAqLj5W3XJ9YUIxZhGlsm0mMRMusmk_Mm8FIRiVIT2FJiglJS2msm0mcT-dITNlHmOuMsCmsh4O4W8pYWOOXRAJobMuMgMWoSMusmk_cPOuMs8uMgMqbi5lImOusmOGuj8qrm0msi9aHWMusmOuMsCObjOGLm8qbRMlsmOusmk_MmgdYZIh4h8KYZXJJmOGLmBxoVApISMusmOuMsCurm0msg5lImOusmOGuj_uMgMSbRMlsmOusmk_ci9uMgMWbRMlsmOusmk_siOGLm5aHWMusmOuMsCurm0msh5lImOusmOGuj_irm0m8i5lImOusmOGujMaLj92siMuMgMqbRMlsmOusmk_siOGLmDRHmOusmOGuj96sm0m8SClsmOusmk_siOGLmClsmOusmk_siOGLmKRHmOusmOG_QOGLmK1YV6NXVMusmk_cPOuMsMe4i5i4W6WLh4K7WMd7XKFImOGLm9uHmOusmOG_QOGLm_tHmOuMsCmMR3t8g7uriASLi4urh82ce7qZe-eoTFZHeOi6e_2qa3mbi7mrmzabiOeYU-lnVApqmzOXRAJobMuMgMqYR7lsmOG_Q;47d4f2569f8c5c00b04e73edd710a93c06bcbf6f908992a3f2c076266481f4c6;tenjKJKT-JoRL1YRI9cQKxIWCeYU_tXW'
    x_api_eid_token = 'jdd03OVCE4YXH2RPNFUS4T4CU5S7UGBXZ5LQMAISF4RQTQGR4A7LJIDF73VLXIWBMQQH5HPLGYOXQMM4C3VRYPK6NKQHCF4AAAAMXW4PDX4AAAAAADMND4KX3WVEQJQX'
    nationalSubsidy_coupons(cookies, h5st, x_api_eid_token)