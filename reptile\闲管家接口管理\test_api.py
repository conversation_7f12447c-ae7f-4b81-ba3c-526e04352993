#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试闲鱼订单接口
"""

import json
import requests
import time
from datetime import datetime

def test_获取订单列表():
    """测试获取订单列表接口"""
    print("=" * 50)
    print("测试获取订单列表接口")
    print("=" * 50)
    
    url = 'https://api.goofish.pro/api/order/pager'
    params = {
        "time_type": 1,
        "time[]": "2024-12-17",
        "time[]": "2025-03-17",
        "idx": 1,
        "size": 25,
    }
    headers = {
        "accept": "application/json, text/plain, */*",
        "accept-encoding": "gzip, deflate, br, zstd",
        "accept-language": "zh-CN,zh;q=0.9",
        "authorization": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTQ0ODE3OTYsImlhdCI6MTc1Mzg3Njk5NiwibmFtZSI6Ikx1Y2tlcl9ZYW4iLCJzaWQiOjY5NzQzNDUwMjg0MDM4OSwidHlwZSI6MSwidWlkIjo2OTc0MzQ1MDI4NDAzOTB9.lJed8-xhejWvCBzcfv6pqIhtMx4UCIUNzJdR2DL_BWU",
        "cache-control": "no-cache",
        "origin": "https://goofish.pro",
        "pragma": "no-cache",
        "priority": "u=1, i",
        "referer": "https://goofish.pro/sale/order/all",
        "sec-ch-ua": '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": '"Windows"',
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-site",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "x-content-security": "key=key=MKGtWHTm9Zh1Q5ue0sC6draLzB3bUJon;secret=XLkJ83KLDnyTstte5HYsrUpqSbNrQmM5CRgIv/OwzvhUm5F2KowX/CgXhBX4mOAWjk4Q/mRKaV6OBpueP8S3LZZQpiUBaupHl6QlJAeYjUNMxq0lgDCvfHKUoW5Im/lFFZTcYoVHzhxULlb0B3aPAJF+NgxLkzrEyRLZcOnNeJ4=;signature=fDG/4wfJ2lrV77+NJ9yogwBnn9mUM16g6fgdT3asEEg=",
        # 添加可能需要的cookie（如果有的话）
        "cookie": ""
    }
    
    try:
        response = requests.get(url, params=params, headers=headers, verify=False, timeout=10)
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print("响应数据:")
                print(json.dumps(data, ensure_ascii=False, indent=2))
                return True, data
            except json.JSONDecodeError as e:
                print(f"JSON解析错误: {e}")
                print(f"原始响应: {response.text}")
                return False, None
        else:
            print(f"请求失败，状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            return False, None
            
    except requests.exceptions.RequestException as e:
        print(f"请求异常: {e}")
        return False, None

def test_获取小程序闲鱼订单():
    """测试获取小程序闲鱼订单接口"""
    print("\n" + "=" * 50)
    print("测试获取小程序闲鱼订单接口")
    print("=" * 50)
    
    url = 'https://h5api.m.goofish.com/h5/mtop.taobao.idle.trade.sold.get/5.0/'
    t = int(time.time()) * 1000
    params = {
        "jsv": '2.7.2',
        "appKey": '********',
        "t": str(t),
        "sign": '68972f1018db039a0baaeb9f332e9e22',
        "api": 'mtop.taobao.idle.trade.sold.get',
        'v': '5.0',
        'dataType': 'json',
        'valueType': 'original',
        'accountSite': 'xianyu',
        'dangerouslySetWindvaneParams': '[object Object]',
        'type': 'originaljson',
        'data': '{"pageNumber":1,"orderStatus":"ALL","offsetRow":0}'
    }
    
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090c11)XWEB/11581",
        "Host": "h5api.m.goofish.com",
        "Content-type": "application/x-www-form-urlencoded",
        "Origin": "https://h5.m.goofish.com",
        "Sec-Fetch-Site": "same-site",
        "Sec-Fetch-Mode": "cors",
        "Sec-Fetch-Dest": "empty",
        "Referer": "https://h5.m.goofish.com/",
        "Accept-Encoding": "gzip, deflate, br",
        "Accept-Language": "zh-CN,zh;q=0.9",
        "cookie": (
            "munb=**********; "
            "unb=**********; "
            "cna=lyNgIKwHDlACAavUZa4nV8J4; "
            "xlly_s=1; "
            "mtop_partitioned_detect=1; "
            f"_m_h5_tk=0bd1598c724d4adb730eb62735aaafb9_{t}; "
            "_m_h5_tk_enc=032720e8eb3f44328608980fc99fcaac; "
            "cookie2=1d3bfbd757e7b7fed1daff781ddd70a8; "
        )
    }
    
    try:
        response = requests.get(url, params=params, headers=headers, verify=False, timeout=10)
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print("响应数据:")
                print(json.dumps(data, ensure_ascii=False, indent=2))
                
                # 检查是否有令牌过期错误
                if 'ret' in data and len(data['ret']) > 0:
                    if "FAIL_SYS_TOKEN_EXOIRED" in data['ret'][0] or "令牌过期" in data['ret'][0]:
                        print("⚠️  令牌已过期，需要重新获取cookie")
                        return False, data
                
                return True, data
            except json.JSONDecodeError as e:
                print(f"JSON解析错误: {e}")
                print(f"原始响应: {response.text}")
                return False, None
        else:
            print(f"请求失败，状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            return False, None
            
    except requests.exceptions.RequestException as e:
        print(f"请求异常: {e}")
        return False, None

def main():
    """主测试函数"""
    print(f"开始测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("正在测试闲鱼订单接口...")
    
    # 测试第一个接口
    success1, data1 = test_获取订单列表()
    
    # 测试第二个接口
    success2, data2 = test_获取小程序闲鱼订单()
    
    # 总结
    print("\n" + "=" * 50)
    print("测试总结")
    print("=" * 50)
    print(f"获取订单列表接口: {'✅ 成功' if success1 else '❌ 失败'}")
    print(f"获取小程序闲鱼订单接口: {'✅ 成功' if success2 else '❌ 失败'}")
    
    if success1 or success2:
        print("\n✅ 至少有一个接口可以正常工作！")
    else:
        print("\n❌ 所有接口都无法正常工作，可能需要更新认证信息")
        print("\n建议:")
        print("1. 检查authorization token是否有效")
        print("2. 更新cookie信息")
        print("3. 检查网络连接")

if __name__ == "__main__":
    main()
