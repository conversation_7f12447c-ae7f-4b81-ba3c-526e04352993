# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'TikTokui.ui'
#
# Created by: PyQt5 UI code generator 5.15.9
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.
import sys

from PyQt5 import QtCore, QtGui, QtWidgets

from PyQt5.QtCore import Qt
from PyQt5.QtWidgets import QScrollArea, QVBoxLayout, QApplication

from reptileTikTok import carryTiktok

class Ui_Form(object):

    def __init__(self, Form):
        self.setupUi(Form)

    def setupUi(self, Form):
        # 配置窗口信息
        Form.setObjectName("Form")
        Form.resize(938, 632)

        # 配置区间文本输入框
        self.line_min = QtWidgets.QLineEdit(Form)
        self.line_min.setGeometry(QtCore.QRect(130, 330, 131, 51))
        self.line_min.setObjectName("line_min")
        self.line_max = QtWidgets.QLineEdit(Form)
        self.line_max.setGeometry(QtCore.QRect(280, 330, 131, 51))
        self.line_max.setObjectName("line_max")

        # 配置抓取人数输入框
        self.line_person = QtWidgets.QLineEdit(Form)
        self.line_person.setGeometry(QtCore.QRect(130, 430, 131, 51))
        self.line_person.setObjectName("person")

        ##  配置抓取人数文本标签
        self.label_person = QtWidgets.QLabel(Form)
        self.label_person.setGeometry(QtCore.QRect(30, 435, 101, 41))
        font = QtGui.QFont()
        font.setFamily("Arial")
        font.setPointSize(12)
        self.label_person.setFont(font)
        self.label_person.setObjectName("label_interval")

        ##  配置区间文本标签
        self.label_interval = QtWidgets.QLabel(Form)
        self.label_interval.setGeometry(QtCore.QRect(30, 335, 101, 41))
        font = QtGui.QFont()
        font.setFamily("Arial")
        font.setPointSize(12)
        self.label_interval.setFont(font)
        self.label_interval.setObjectName("label_interval")

        ##  配置区间文本标签
        self.label_sign = QtWidgets.QLabel(Form)
        self.label_sign.setGeometry(QtCore.QRect(260, 330, 31, 51))
        font = QtGui.QFont()
        font.setFamily("Arial")
        font.setPointSize(14)
        font.setBold(True)
        font.setWeight(75)
        self.label_sign.setFont(font)
        self.label_sign.setObjectName("label_sign")
        # 配置登录按钮
        self.button_login = QtWidgets.QPushButton(Form)
        self.button_login.setGeometry(QtCore.QRect(640, 330, 141, 51))
        font = QtGui.QFont()
        font.setFamily("Arial")
        font.setPointSize(10)
        self.button_login.setFont(font)
        self.button_login.setTabletTracking(False)
        self.button_login.setContextMenuPolicy(QtCore.Qt.NoContextMenu)
        self.button_login.setStyleSheet("")
        self.button_login.setDefault(False)
        self.button_login.setObjectName("button_login")

        # 配置导出excel按钮
        self.button_export_excel = QtWidgets.QPushButton(Form)
        self.button_export_excel.setGeometry(QtCore.QRect(790, 330, 141, 51))
        font = QtGui.QFont()
        font.setFamily("Arial")
        font.setPointSize(10)
        self.button_export_excel.setFont(font)
        self.button_export_excel.setTabletTracking(False)
        self.button_export_excel.setContextMenuPolicy(QtCore.Qt.NoContextMenu)
        self.button_export_excel.setStyleSheet("")
        self.button_export_excel.setDefault(False)
        self.button_export_excel.setObjectName("button_export_excel")

        # 配置消息窗口
        self.label_msg = QtWidgets.QLabel(Form)
        self.label_msg.setGeometry(QtCore.QRect(10, 10, 921, 311))
        font = QtGui.QFont()
        font.setFamily("Arial")
        font.setPointSize(72)
        self.label_msg.setFont(font)
        self.label_msg.setObjectName("label_msg")
        self.button_reptile = QtWidgets.QPushButton(Form)
        self.button_reptile.setGeometry(QtCore.QRect(490, 330, 141, 51))
        font = QtGui.QFont()
        font.setFamily("Arial")
        font.setPointSize(10)
        self.button_reptile.setFont(font)
        self.button_reptile.setTabletTracking(False)
        self.button_reptile.setContextMenuPolicy(QtCore.Qt.NoContextMenu)
        self.button_reptile.setStyleSheet("")
        self.button_reptile.setDefault(False)
        self.button_reptile.setObjectName("button_reptile")

        self.retranslateUi(Form)
        QtCore.QMetaObject.connectSlotsByName(Form)

    def retranslateUi(self, Form):
        _translate = QtCore.QCoreApplication.translate
        Form.setWindowTitle(_translate("Form", "Form"))
        self.label_interval.setText(_translate("Form", "粉丝区间："))
        self.label_person.setText(_translate("Form", "抓取人数："))

        self.label_sign.setText(_translate("Form", "→"))
        self.button_login.setText(_translate("Form", "登录TikTok"))
        self.button_export_excel.setText(_translate("Form", "导出数据到excel"))
        self.label_msg.setText(_translate("Form", "TextLabel"))
        self.button_reptile.setText(_translate("Form", "开始爬取"))


class MyWindow(QtWidgets.QWidget):
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.ct = carryTiktok()
        self.msg_histroy = list()


    def init_ui(self):
        self.ui = Ui_Form(self)

        # 区间文本框
        self.line_min = self.ui.line_min
        self.line_max = self.ui.line_max
        self.line_person = self.ui.line_person
        # 区间标签
        self.label_sign = self.ui.label_sign
        self.label_interval = self.ui.label_interval
        self.label_person = self.ui.label_person

        # 消息
        self.label_msg = self.ui.label_msg
        self.label_msg.setWordWrap(True)
        # 配置滚动条
        self.scroll_msg = QScrollArea(self)
        self.scroll_msg.setWidget(self.label_msg)
        self.scroll_msg.setGeometry(QtCore.QRect(10, 10, 921, 311))
        self.label_msg.setAlignment(Qt.AlignTop)
        self.label_msg.setObjectName("")
        v_layout = QVBoxLayout()
        v_layout.addWidget(self.scroll_msg)

        # 登录按钮
        self.button_login = self.ui.button_login
        self.button_login.clicked.connect(self.login_ui)

        # 数据抓取按钮
        self.button_reptile = self.ui.button_reptile
        self.button_reptile.clicked.connect(self.reptile_ui)

        # 导出excel按钮
        self.button_export_excel = self.ui.button_export_excel
        self.button_export_excel.clicked.connect(self.export_ui)


    def login_ui(self):
        # 创建登录按钮
        ct = carryTiktok()
        ct_msg = ct.login()
        font = QtGui.QFont()
        font.setFamily("Arial")
        font.setPointSize(14)
        font.setBold(True)
        font.setWeight(18)
        self.label_msg.setFont(font)
        self.label_msg.setText(ct_msg)  # 打印消息
        self.label_msg.repaint()  # 重新渲染

    def reptile_ui(self):
        data_msg = self.ct.getData(reviewer_max_num=float(self.line_max.text()), reviewer_min_num=float(self.line_min.text()),person=int(self.line_person.text()))
        font = QtGui.QFont()
        font.setFamily("Arial")
        font.setPointSize(14)
        font.setBold(True)
        font.setWeight(14)
        self.label_msg.setFont(font)
        for i in data_msg:
            self.msg_histroy.append("抓取【"+i['anchorId']+"】>>>成功")
            self.label_msg.setText("<br>".join(self.msg_histroy))
            self.label_msg.resize(921, self.label_msg.frameSize().height()+16)
            self.label_msg.repaint()

    def export_ui(self):
        export_msg = self.ct.export_excel()
        font = QtGui.QFont()
        font.setFamily("Arial")
        font.setPointSize(14)
        font.setBold(True)
        font.setWeight(18)
        self.label_msg.setFont(font)
        self.label_msg.setText(export_msg)  # 打印消息
        self.label_msg.repaint()  # 重新渲染


