import os, sys, pickle

from time import sleep
import pandas as pd

from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By


class aiqicha:
    def __init__(self):
        self.BASE_DIR = os.path.dirname(os.path.realpath(sys.argv[0]))

        options = Options()
        options.add_experimental_option("debuggerAddress", "127.0.0.1:9527")
        s = Service(executable_path=os.path.join(self.BASE_DIR, "utils\\chromedriver.exe"))
        self.bro = webdriver.Chrome(service=s, options=options)
        self.company_data = []
        self.school_data = []

    # 公司抓取
    def company_reptile(self):
        self.bro.get(
            "https://aiqicha.baidu.com/s?q=%E4%B8%AD%E7%A7%91%E9%99%A2%E7%A6%8F%E5%BB%BA%E7%89%A9%E8%B4%A8%E7%BB%93%E6%9E%84%E7%A0%94%E7%A9%B6%E6%89%80&t=0")
        company = ['安派智厨(浙江)有限公司',
'百明信康生物技术(浙江)有限公司',
'北平机床(浙江)股份有限公司',
'奔腾激光(浙江)股份有限公司',
'德玛克(浙江)精工科技有限公司',
'菲耀科技(浙江)有限公司',
'赋同量子科技(浙江)有限公司',
'公元电器(浙江)有限公司',
'国电浙江北仑第三发电有限公司',
'国电浙江北仑第一发电有限公司',
'国网浙江安吉县供电公司',
'国网浙江常山县供电公司',
'国网浙江淳安县供电公司',
'国网浙江慈溪市供电公司',
'国网浙江德清县供电公司',
'国网浙江东阳市供电公司',
'国网浙江奉化市供电公司',
'国网浙江富阳市供电公司',
'国网浙江海宁市供电公司',
'国网浙江海盐县供电公司',
'国网浙江杭州市富阳区供电公司',
'国网浙江杭州市萧山区供电公司',
'国网浙江杭州市余杭区供电公司',
'国网浙江嘉善县供电公司',
'国网浙江建德市供电公司',
'国网浙江江山市供电公司',
'国网浙江缙云县供电公司',
'国网浙江缙云县供电有限公司',
'国网浙江景宁县供电公司',
'国网浙江开化县供电公司',
'国网浙江兰溪市供电公司',
'国网浙江乐清市供电公司',
'国网浙江乐清市供电公司乐清市电力实业公司',
'国网浙江临安市供电公司',
'国网浙江临海市供电公司',
'国网浙江龙游县供电公司',
'国网浙江宁波市鄞州区供电公司',
'国网浙江宁海县供电公司',
'国网浙江平湖市供电公司',
'国网浙江浦江县供电公司',
'国网浙江青田县供电公司',
'国网浙江庆元县供电公司',
'国网浙江瑞安市供电有限责任公司',
'国网浙江三门县供电公司',
'国网浙江上虞区供电公司',
'国网浙江上虞市供电公司',
'国网浙江绍兴市上虞区供电公司',
'国网浙江省电力电力科学研究院',
'国网浙江省电力公司',
'国网浙江省电力公司岱山县供电公司',
'国网浙江省电力公司电力科学研究院',
'国网浙江省电力公司洞头县供电公司',
'国网浙江省电力公司杭州供电公司',
'国网浙江省电力公司湖州供电公司',
'国网浙江省电力公司嘉兴供电公司',
'国网浙江省电力公司检修分公司',
'国网浙江省电力公司金华供电公司',
'国网浙江省电力公司紧水滩水力发电厂',
'国网浙江省电力公司经济技术研究院',
'国网浙江省电力公司景宁县供电公司',
'国网浙江省电力公司龙泉市供电公司',
'国网浙江省电力公司宁波供电公司',
'国网浙江省电力公司磐安县供电公司',
'国网浙江省电力公司培训中心',
'国网浙江省电力公司培训中心浙西分中心',
'国网浙江省电力公司庆元县供电公司',
'国网浙江省电力公司绍兴供电公司',
'国网浙江省电力公司嵊泗县供电公司',
'国网浙江省电力公司台州供电公司',
'国网浙江省电力公司泰顺县供电公司',
'国网浙江省电力公司文成县供电公司',
'国网浙江省电力公司信息通信分公司',
'国网浙江省电力公司舟山供电公司',
'国网浙江嵊州市供电公司',
'国网浙江松阳县供电公司',
'国网浙江遂昌县供电公司',
'国网浙江台州市黄岩区供电公司',
'国网浙江台州市路桥区供电公司',
'国网浙江天台县供电公司',
'国网浙江桐庐县供电公司',
'国网浙江桐乡市供电公司',
'国网浙江温岭市供电公司',
'国网浙江武义县供电公司',
'国网浙江仙居县供电公司',
'国网浙江象山县供电公司',
'国网浙江新昌县供电公司',
'国网浙江义乌市供电公司',
'国网浙江永嘉县供电公司',
'国网浙江永康市供电公司',
'国网浙江余姚市供电公司',
'国网浙江云和县供电公司',
'国网浙江长兴县供电公司',
'国网浙江诸暨市供电公司',
'华泽包装制品(浙江)有限公司',
'吉利聚能(浙江)科技有限公司',
'交信北斗(浙江)科技有限公司',
'理想晶延半导体设备(浙江)有限公司',
'迈珑(浙江)塑业有限公司',
'尼得科精密检测设备(浙江)有限公司',
'尼得科智能装备(浙江)有限公司',
'品康(浙江)电子商务有限公司',
'锐创新能源(浙江)有限公司',
'瑞尔明康(浙江)医疗科技有限公司',
'微牌科技(浙江)有限公司',
'医联网(浙江)技术股份有限公司',
'盈德气体工程(浙江)有限公司',
'优耐德(浙江)科技股份有限公司',
'元颉新材料科技(浙江)有限公司',
'浙江阿莱西澳新材料科技有限公司',
'浙江爱仕达电器股份有限公司',
'浙江安存云链数据技术有限公司',
'浙江百诺数智环境科技股份有限公司',
'浙江百翔科技股份有限公司',
'浙江佰辰低碳科技有限公司',
'浙江佰年新材料科技有限公司',
'浙江宝丰特材股份有限公司',
'浙江贝斯曼缝纫机有限公司',
'浙江博达汽车零部件有限公司',
'浙江灿根智能科技有限公司',
'浙江常山科润新材料有限公司',
'浙江超晟科技有限公司',
'浙江辰珂西传动机械有限公司',
'浙江诚通市政建设有限公司',
'浙江城建煤气热电设计院股份有限公司',
'浙江驰宇空天技术有限公司',
'浙江出入境检验检疫局检验检疫技术中心',
'浙江传化日用品有限公司',
'浙江传媒学院',
'浙江创维自动化工程有限公司',
'浙江大邦运动器材有限公司',
'浙江大立长光光电科技有限公司',
'浙江大起电子股份有限公司',
'浙江大自然旅游用品有限公司',
'浙江丹山安防设备科技有限公司',
'浙江道可道楼道电梯有限公司',
'浙江得伟科技股份有限公司',
'浙江德翰动力系统有限公司',
'浙江德硕科技股份有限公司',
'浙江德西瑞无纺科技有限公司',
'浙江地球村环保科技有限公司',
'浙江电力电子技术公司',
'浙江电力建设监理有限公司',
'浙江尔友服饰有限公司',
'浙江梵力得健康科技有限公司',
'浙江方远建材科技有限公司',
'浙江纺织服装职业技术学院',
'浙江丰瑜生态科技有限公司',
'浙江福达合金材料科技有限公司',
'浙江富阳市新源交通电子有限公司',
'浙江富翼科技有限公司',
'浙江高精锻压股份有限公司',
'浙江高信技术股份有限公司',
'浙江高裕家居科技有限公司',
'浙江格贝能源科技有限公司',
'浙江格尔泰斯环保特材科技有限公司',
'浙江工贸职业技术学院',
'浙江工业职业技术学院',
'浙江古婺窑火陶瓷文化有限公司',
'浙江光大合一文具有限公司',
'浙江广环工贸有限公司',
'浙江广厦建设职业技术学院',
'浙江国华余姚燃气发电有限责任公司',
'浙江国华浙能发电有限公司',
'浙江国际海运职业技术学院',
'浙江国际旅行卫生保健中心(浙江出入境检验检疫局口岸门诊部)',
'浙江国松家居股份有限公司',
'浙江国自机器人技术有限公司',
'浙江海畅气体股份有限公司',
'浙江海力生生物科技有限公司',
'浙江海泰融智声学科技有限公司',
'浙江海洋学院',
'浙江汉风智能科技有限公司',
'浙江杭摩合成材料股份有限公司',
'浙江杭森制冷设备有限公司',
'浙江杭萧钢构股份有限公司',
'浙江和易瑞京科技发展有限公司',
'浙江和元建设有限公司',
'浙江荷鹭乳业有限公司',
'浙江亨达塑料模具有限公司',
'浙江恒河石油化工股份有限公司',
'浙江恒业电子股份有限公司',
'浙江恒逸石化研究院有限公司',
'浙江宏发电声有限公司',
'浙江鸿屹智能装备科技有限公司',
'浙江湖州物装综合体管理服务有限责任公司',
'浙江沪韧科技有限公司',
'浙江花园药业有限公司',
'浙江华邦物联技术股份有限公司',
'浙江华昌新材料股份有限公司',
'浙江华电器材检测研究所',
'浙江华电器材检测研究所有限公司',
'浙江华电乌溪江水力发电厂',
'浙江华峰氨纶股份有限公司',
'浙江华江科技发展有限公司',
'浙江华力管业有限公司',
'浙江华立生命科技有限公司',
'浙江华泰塑胶股份有限公司',
'浙江华新实业有限公司',
'浙江华义医药有限公司',
'浙江华益精密机械有限公司',
'浙江桓宇汽配有限公司',
'浙江皇马新材料科技有限公司',
'浙江辉煌三联实业股份有限公司',
'浙江汇能动物药品有限公司',
'浙江慧谷信息技术有限公司',
'浙江机电职业技术学院',
'浙江吉利新能源商用车有限公司',
'浙江加州国际纳米技术研究院台州分院',
'浙江嘉科智造科技有限公司',
'浙江嘉欣金三塔丝针织有限公司',
'浙江建鑫型钢科技股份有限公司',
'浙江建研科之杰新材料有限公司',
'浙江建装工程技术研究有限公司',
'浙江江山化工股份有限公司',
'浙江杰翎健康科技有限公司',
'浙江金盾压力容器智造股份有限公司',
'浙江金火机床有限公司',
'浙江金洁环境股份有限公司',
'浙江金利华电气设备有限公司',
'浙江金手宝生物科技有限公司',
'浙江锦盛装饰材料股份有限公司',
'浙江精安再生资源发展有限公司',
'浙江精工钢结构有限公司',
'浙江精工集成科技股份有限公司',
'浙江景迈环境科技有限公司',
'浙江九川竹木有限公司',
'浙江九紫建设有限公司',
'浙江久功新能源科技有限公司',
'浙江巨人控股有限公司',
'浙江钜丰科技股份有限公司',
'浙江锯力煌工业科技股份有限公司',
'浙江开尔实业有限公司',
'浙江开能润鑫电器有限公司',
'浙江凯弛电子科技有限公司',
'浙江凯恩电池有限公司',
'浙江凯瑞博科技股份有限公司',
'浙江凯润制药有限公司',
'浙江凯耀照明股份有限公司',
'浙江康贝尔实业有限公司',
'浙江康恩贝健康产品有限公司',
'浙江康锐环境科技有限公司',
'浙江科畅电子科技有限公司',
'浙江科技学院',
'浙江科技学院(浙江中德科技促进中心)',
'浙江库实健康科技有限公司',
'浙江快易智能制造有限公司',
'浙江莱美纺织印染科技有限公司',
'浙江蓝美科技股份有限公司',
'浙江蓝能氢能科技股份有限公司',
'浙江蓝天环保氟材料有限公司',
'浙江蓝也科技股份有限公司',
'浙江浪莎针织有限公司',
'浙江乐欣户外用品有限公司',
'浙江励泰智能机械有限公司',
'浙江利丰电器有限公司',
'浙江利兴建材有限公司',
'浙江隆源装备科技股份有限公司',
'浙江陆恒环境科技有限公司',
'浙江路港工程咨询有限公司',
'浙江绿城都会建筑规划设计有限公司',
'浙江绿创生物科技有限公司',
'浙江绿维环境科技有限公司',
'浙江绿欣环卫设备有限公司',
'浙江脉通智造科技(集团)有限公司',
'浙江美佳尼自动化设备有限公司',
'浙江美科斯叉车有限公司',
'浙江美新控股有限公司',
'浙江名瑞智能包装科技有限公司',
'浙江农业商贸职业学院',
'浙江欧格自动化科技有限公司',
'浙江欧霖涂料有限公司',
'浙江欧路莎贸易有限公司',
'浙江欧伦电气股份有限公司',
'浙江欧赛思生物科技有限公司',
'浙江帕博塑料制品有限公司',
'浙江派威数字技术有限公司',
'浙江澎湃轨道科技有限公司',
'浙江齐元机器人有限公司',
'浙江启冠网络股份有限公司',
'浙江启明药业有限公司',
'浙江清科电力科技有限公司',
'浙江仁栋电气科技有限公司',
'浙江荣义新材料科技有限公司',
'浙江融创信息产业有限公司',
'浙江锐敏生活科技有限公司',
'浙江瑞明节能门窗技术有限公司',
'浙江瑞堂塑料科技有限公司',
'浙江瑞旭过滤技术股份有限公司',
'浙江三丰建设有限公司',
'浙江三花换热器有限公司',
'浙江森歌智能厨电股份有限公司',
'浙江森禾种业股份有限公司',
'浙江森马生态农业发展有限公司',
'浙江沙星科技股份有限公司',
'浙江山下湖珍珠集团股份有限公司',
'浙江山峪染料化工有限公司',
'浙江商业职业技术学院',
'浙江升华拜克生物股份有限公司',
'浙江省泵阀产品质量检验中心',
'浙江省博物馆',
'浙江省大成建设集团有限公司',
'浙江省淡水水产研究所',
'浙江省德清县浦森耐火材料有限公司',
'浙江省地质调查院',
'浙江省地质矿产研究所',
'浙江省电力公司',
'浙江省电力公司电力科学研究院',
'浙江省电力公司紧水滩水力发电厂',
'浙江省电力公司舟山电力局',
'浙江省电力设计院',
'浙江省电力试验研究院',
'浙江省电力试验研究院技术服务中心',
'浙江省发展新型墙体材料办公室',
'浙江省柑桔研究所',
'浙江省港航管理局',
'浙江省公路管理局',
'浙江省海洋开发研究院',
'浙江省海洋水产养殖研究所',
'浙江省宏途交通建设有限公司',
'浙江省环境保护科学设计研究院',
'浙江省疾病预防控制中心',
'浙江省嘉兴市农业科学研究院(所)',
'浙江省建设机械集团有限公司',
'浙江省交通工程建设集团有限公司',
'浙江省交通建设工程监督管理局',
'浙江省交通科学研究院',
'浙江省林业技术推广总站(浙江省林业信息宣传中心)',
'浙江省能源与核技术应用研究院',
'浙江省农药检定管理所',
'浙江省农业科学院园艺研究所',
'浙江省食品药品检验研究院',
'浙江省水产技术推广总站',
'浙江省水利河口研究院',
'浙江省水利水电勘测设计院有限责任公司',
'浙江省水资源管理中心',
'浙江省送变电工程公司',
'浙江省泰顺县聚俄高分子材料应用研究所',
'浙江省特种设备检验研究院',
'浙江省通用砂浆研究院',
'浙江省萧山棉麻研究所',
'浙江省亚热带作物研究所',
'浙江省岩土基础公司',
'浙江省沼气太阳能科学研究所',
'浙江省质量检测科学研究院',
'浙江圣大新材料有限公司',
'浙江世博新材料有限公司',
'浙江世道电器有限公司',
'浙江仕雅达纺织股份有限公司',
'浙江树人学院',
'浙江双林机械股份有限公司',
'浙江水利水电学院',
'浙江顺艺园林工程有限公司',
'浙江斯泰信息科技有限公司',
'浙江松杉电力科技股份有限公司',
'浙江苏可安药业有限公司',
'浙江苏拉新材料科技股份有限公司',
'浙江台玖精密机械股份有限公司',
'浙江泰美工贸有限公司',
'浙江碳足迹科技集团有限公司',
'浙江特种电机股份有限公司',
'浙江天际互感器股份有限公司',
'浙江天蓝环保技术有限公司',
'浙江天马轴承有限公司',
'浙江陀曼数字技术有限公司',
'浙江万安智驭汽车控制系统有限公司',
'浙江万达汽车方向机有限公司',
'浙江万朋数智科技股份有限公司',
'浙江万向太阳能有限公司',
'浙江望高科技发展有限公司',
'浙江威尔森新材料有限公司',
'浙江威克环境艺术工程有限公司',
'浙江维都利阀门制造有限公司',
'浙江温兄机械阀业有限公司',
'浙江沃曼新材料科技有限公司',
'浙江沃坦科水暖设备科技股份有限公司',
'浙江无道科技有限公司',
'浙江希尔机器人有限公司',
'浙江熙家橡塑科技有限公司',
'浙江霞典纺织有限公司',
'浙江仙鹤特种纸有限公司',
'浙江贤烨新材料科技有限公司',
'浙江翔龙航空科技有限公司',
'浙江小遛信息科技有限公司',
'浙江小伦智能制造股份有限公司',
'浙江小宇科技股份有限公司',
'浙江芯科半导体有限公司',
'浙江芯唐浩志智能科技有限公司',
'浙江欣海船舶设计研究院有限公司',
'浙江新东港药业股份有限公司',
'浙江新时代中能科技股份有限公司',
'浙江新涛电子机械股份有限公司',
'浙江新星机械工程有限公司',
'浙江新中南汽车零部件股份有限公司',
'浙江鑫联新能源科技有限公司',
'浙江鑫淼纺织有限公司',
'浙江鑫益威科技有限公司',
'浙江兴茂致尚科技有限公司',
'浙江兴艺环境建设有限公司',
'浙江旭宏电子有限公司',
'浙江旭森阻燃剂股份有限公司',
'浙江迅达工业科技股份有限公司',
'浙江雅阁集成吊顶有限公司',
'浙江亚特电器股份有限公司',
'浙江亚特新材料有限公司',
'浙江业友机械有限公司',
'浙江伊森爱家纺有限公司',
'浙江医链医疗科技有限公司',
'浙江医药高等专科学校',
'浙江医院',
'浙江宜通华盛科技有限公司',
'浙江亿承实业有限公司',
'浙江亿东工程科技有限公司',
'浙江易煌科技有限公司',
'浙江懿纱纺织科技有限公司',
'浙江宇龙药业有限公司',
'浙江元成园林集团股份有限公司',
'浙江越剑机械制造有限公司',
'浙江赞宇科技股份有限公司',
'浙江增洲造船有限公司',
'浙江展帆实业有限公司',
'浙江长江汽车电子有限公司',
'浙江长兴昆腾智能装备有限公司',
'浙江浙大网新能源技术有限公司',
'浙江浙大之光照明技术研究有限公司',
'浙江浙电经济技术研究院',
'浙江振申绝热科技有限公司',
'浙江振新玻璃股份有限公司',
'浙江振鑫新材料科技有限公司',
'浙江正辉针织科技股份有限公司',
'浙江正力安拓生物科技有限公司',
'浙江正泰鑫辉光伏有限公司',
'浙江正泰智维能源服务有限公司',
'浙江正雅齿科股份有限公司',
'浙江致远环境科技股份有限公司',
'浙江中电建钱塘勘测设计研究院有限公司',
'浙江中控太阳能技术有限公司',
'浙江中控信息产业股份有限公司',
'浙江中锂电科技有限公司',
'浙江中南绿建科技集团有限公司',
'浙江中诺智能机械有限公司',
'浙江中天东方氟硅材料股份有限公司',
'浙江中屹缝纫机有限公司',
'浙江中哲新能源有限公司',
'浙江舟山启明电力集团公司海缆工程公司',
'浙江珠龙包装科技有限公司',
'浙江左易电力设备有限公司',
'浙理(浙江)检测技术有限公司',
'中科致良新能源材料(浙江)有限公司',
'安迈森(福建)线缆有限公司',
'福建半亩方塘生物科技有限公司',
'福建宝翔针织技术股份有限公司',
'福建必冠芯电科技股份有限公司',
'福建出入境检验检疫局检验检疫技术中心',
'福建春伦茶业集团有限公司',
'福建大北农华有水产科技集团有限公司',
'福建迪斯发科技有限公司',
'福建帝盛科技股份有限公司',
'福建广弘翔建设有限公司',
'福建广生中霖生物科技有限公司',
'福建国际旅行卫生保健中心',
'福建海西新药创制股份有限公司',
'福建海源自动化机械股份有限公司',
'福建和盛置信非晶合金变压器有限公司',
'福建嘉能光电科技有限公司',
'福建江夏学院',
'福建交通职业技术学院',
'福建科之杰新材料有限公司',
'福建力霸机械科技股份有限公司',
'福建林业职业技术学院',
'福建龙祥建设集团有限公司',
'福建闽联信息科技有限公司',
'福建南方路面机械有限公司',
'福建农大科技开发总公司',
'福建奇迹运动体育科技有限公司',
'福建侨龙应急装备有限公司',
'福建冉晟电气有限公司',
'福建荣建建设工程集团有限公司',
'福建容钠新能源科技有限公司',
'福建三鲸消防技术研究院有限公司',
'福建生物工程职业技术学院',
'福建省121地质大队',
'福建省爱善环保科技有限公司',
'福建省产品质量检验研究院',
'福建省大田县供电有限公司',
'福建省德奥针织股份有限公司',
'福建省地质工程勘察院',
'福建省第二电力建设公司',
'福建省第一公路工程公司',
'福建省电力勘测设计院',
'福建省电力有限公司',
'福建省电力有限公司电力科学研究院',
'福建省电力有限公司福建和盛高科技产业有限公司',
'福建省电力有限公司福建省电力有限公司电力科学研究院',
'福建省电力有限公司福建省电力有限公司检修分公司',
'福建省电力有限公司福建省电力有限公司培训中心',
'福建省电力有限公司福建水口发电集团有限公司',
'福建省电力有限公司检修分公司',
'福建省电力有限公司培训中心',
'福建省港航管理局',
'福建省港航勘察设计研究院',
'福建省高速公路有限责任公司',
'福建省光电子技术重点实验室',
'福建省环境科学研究院',
'福建省计量科学研究院',
'福建省建宁县供电有限公司',
'福建省晋江市电力有限责任公司',
'福建省连城县供电有限公司',
'福建省林木种苗总站',
'福建省林业科学研究院',
'福建省曼玲食品股份有限公司',
'福建省闽量校准技术中心有限公司',
'福建省明溪县供电有限公司',
'福建省明溪智汇电子商务有限公司',
'福建省农业机械化研究所',
'福建省农业科学院科技干部培训中心',
'福建省农业科学院农业工程技术研究所',
'福建省农业科学院农业生物资源研究所',
'福建省农业科学院生物技术研究所',
'福建省农业科学院食用菌研究所',
'福建省农业科学院中心实验室',
'福建省农业区划研究所',
'福建省普华电子科技有限公司',
'福建省汽车工业集团云度新能源汽车股份有限公司',
'福建省石狮电力有限责任公司',
'福建省水产饲料研究会',
'福建省水产研究所',
'福建省水利水电勘测设计研究院有限公司',
'福建省松溪县永顺机械有限公司',
'福建省泰宁县供电有限公司',
'福建省特种设备检验研究院',
'福建省土地开发整理中心',
'福建省纤维检验局',
'福建省亚南科技股份有限公司',
'福建省亚热带植物研究所',
'福建省洋口国有林场',
'福建省永安市供电有限公司',
'福建省永富建设集团有限公司',
'福建省永建皮革科技股份有限公司',
'福建省中霖工程建设有限公司',
'福建省中医药研究院',
'福建省中医药研究院(福建省青草药开发服务中心)',
'福建斯狄渢电开水器有限公司',
'福建卫东实业股份有限公司',
'福建沃豪科技集团有限公司',
'福建武夷烟叶有限公司',
'福建鑫创好钢结构有限公司',
'福建移光能源科技股份有限公司',
'福建义精科技有限公司',
'福建永福电通技术开发有限公司',
'福建优益佳食品科技有限公司',
'福建宇杭建设发展有限公司',
'福建跃凯科技有限公司',
'福建柘参生物科技研究股份有限公司',
'福建中庚信息科技有限公司',
'福建中试所电力实业有限公司',
'冠捷电子科技(福建)有限公司',
'囯网福建省电力有限公司经济技术研究院',
'国网福建电力有限公司电力科学研究院',
'国网福建节能服务有限公司',
'国网福建省电力有限公司超高压分公司',
'国网福建永定县供电有限公司',
'宏峰集团(福建)有限公司',
'健乐高(福建)生物科技有限公司',
'骆驼(福建)户外用品有限公司',
'茂泰(福建)新材料科技有限公司',
'天和骏行智能装备(福建)有限公司',
'鑫京瑞钨钢(福建)有限公司',
'怡佳(福建)卫生用品股份有限公司',
'银捷尼科(福建)科技有限公司',
'永春县产品质量检验所(福建省香产品质量检验中心、国家燃香类产品质量监督检验中心(福建)',
'中国石油化工股份有限公司福建石油分公司',
'中国烟草总公司福建省分公司',
'囯网江西省电力科学研究院',
'国网江西省电力公司',
'国网江西省电力公司德安县供电分公司',
'国网江西省电力公司电力科学研究院',
'国网江西省电力公司分宜县供电分公司',
'国网江西省电力公司丰城市供电分公司',
'国网江西省电力公司赣西供电分公司',
'国网江西省电力公司高安市供电分公司',
'国网江西省电力公司吉安供电分公司',
'国网江西省电力公司检修分公司',
'国网江西省电力公司经济技术研究院',
'国网江西省电力公司景德镇供电分公司',
'国网江西省电力公司九江供电分公司',
'国网江西省电力公司九江市濂溪区供电分公司',
'国网江西省电力公司莲花县供电分公司',
'国网江西省电力公司庐山市供电分公司',
'国网江西省电力公司南昌供电分公司',
'国网江西省电力公司培训中心',
'国网江西省电力公司彭泽县供电分公司',
'国网江西省电力公司萍乡供电分公司',
'国网江西省电力公司瑞昌市供电分公司',
'国网江西省电力公司峡江县供电分公司',
'国网江西省电力公司新余市渝水区供电分公司',
'国网江西省电力公司信息通信分公司',
'国网江西省电力公司宜春供电分公司',
'国网江西省电力公司樟树市供电分公司',
'国网江西省电力科学研究院新冶高科技集团有限公司',
'国网江西省电力有限公司电力科学研究',
'国网江西省电力有限公司赣西供电分公司',
'国网江西新建县供电有限责任公司',
'回音必集团(江西)东亚制药有限公司',
'江西安天高新材料股份有限公司',
'江西博沃新材料有限公司',
'江西博鑫环保科技股份有限公司',
'江西车仆电子智能科技有限公司',
'江西晟琪科技股份有限公司',
'江西电力职业技术学院',
'江西公路开发总公司',
'江西硅瀛新能源科技有限公司',
'江西济民可信金水宝制药有限公司',
'江西佳信房地产资产评估有限公司',
'江西江中制药(集团)有限责任公司',
'江西金格科技有限公司',
'江西金域医学检验实验室有限公司',
'江西缙禧纳米材料有限公司',
'江西晶科电子股份有限公司',
'江西景兴医疗器械有限公司',
'江西九峰纳米科技有限公司',
'江西埾鑫电气有限公司',
'江西康替龙竹叶有限公司',
'江西绿蓝创新建材有限公司',
'江西玛哈特智能科技有限公司',
'江西磨铁科技有限公司',
'江西农业工程职业学院',
'江西欧菲光学有限公司',
'江西奇信集团股份有限公司',
'江西前江信息技术有限公司',
'江西权健日用品有限公司',
'江西仁仁健康微生态科技有限公司',
'江西省蚕桑茶叶研究所',
'江西省超级水稻研究发展中心',
'江西省电力公司',
'江西省电力公司电动汽车服务分公司',
'江西省电力公司电力经济技术研究院',
'江西省电力公司信息通信分公司',
'江西省电力科学研究院',
'江西省赣鑫纺织有限公司',
'江西省高速公路联网管理中心',
'江西省高速公路投资集团有限责任公司',
'江西省公路机械工程局',
'江西省红壤研究所',
'江西省化学工业研究所',
'江西省计算技术研究所',
'江西省建材科研设计院有限公司',
'江西省江铜-台意特种电工材料有限公司',
'江西省江铜铜箔科技股份有限公司',
'江西省江铜-耶兹铜箔有限公司',
'江西省科学学院应用物理研究所',
'江西省科学院',
'江西省科学院能源研究所',
'江西省科学院微生物研究所',
'江西省空间生态建设有限公司',
'江西省粮油科学技术研究所',
'江西省林业科学院',
'江西省梦江南农场有限公司',
'江西省水产科学研究所',
'江西省水利科学研究院',
'江西省烟叶科学研究所',
'江西省宜春市农业科学研究所',
'江西思辰信息技术有限公司',
'江西碳金科技有限公司',
'江西天成锂业有限公司',
'江西天地人环保科技(集团)有限公司',
'江西铜业集团公司',
'江西稀有金属钨业控股集团有限公司',
'江西犀瑞制造有限公司',
'江西心连心化学工业有限公司',
'江西新节氢能源科技有限公司',
'江西新胜新材料有限公司',
'江西鑫金晖智能科技有限公司',
'江西鑫淘科技股份有限公司',
'江西星星科技股份有限公司',
'江西兴泰科技股份有限公司',
'江西益昕葆生物科技有限公司',
'江西银涛药业股份有限公司',
'江西应用技术职业学院',
'江西宇能制药股份有限公司',
'江西正宇建设集团有限公司',
'江西知产大数据有限公司',
'江西志超教育装备集团有限公司',
'江西中轻智能设备有限公司',
'江西中医学院',
'捷德(江西)技术有限公司',
'中国科学院江西稀土研究院',
'中国石油化工股份有限公司江西石油分公司'
]
        # import tools
        # company_data = tools.load_json_name("公司[无].txt")
        # for i in company_data:
        #     if i['name'] != '':
        #         company.append(i['name'])

        for i in company:
            data_dict = dict()
            data_dict["search"] = i
            search_input_ex = self.bro.find_element(By.XPATH, "//input[@id='aqc-header-search-input']")
            search_input_ex.clear()
            search_input_ex.send_keys(i)

            search_button_ex = self.bro.find_element(By.XPATH,
                                                     "//button[@class='search-btn' and contains(text(), '搜索')]")
            search_button_ex.click()

            # 处理检测机制
            sleep(10)
            for i in range(1, 720):
                if True:
                    try:
                        self.bro.find_element(By.XPATH,
                                              "//div[contains(@class,'mod-vcode-content-mkd')]/p[text()='百度安全验证']")
                        sleep(1)
                        print("请进行人工验证")
                    except:
                        break

            try:
                company_title_ex = self.bro.find_element(By.XPATH,
                                                         "//div[@class='company-list']/div[1]//h3[@class='title']/a[1]")
                company_title = company_title_ex.get_attribute("title")
                data_dict['company_title'] = company_title
            except:
                company_title = "null"
                data_dict['company_title'] = company_title

            # 成立时间
            try:
                company_date_ex = self.bro.find_element(By.XPATH,
                                                        "//div[@class='company-list']/div[1]//span[@class='pre-title' and text()='成立时间：']/following-sibling::span")
                company_date = company_date_ex.text
                data_dict['company_date'] = company_date
            except:
                company_date = "null"
                data_dict['company_date'] = company_date

            # 地址
            try:
                company_address_ex = self.bro.find_element(By.XPATH,
                                                           "//div[@class='company-list']/div[1]//span[@class='pre-title' and text()='地址：']/following-sibling::span")
                company_address = company_address_ex.text
                data_dict['company_address'] = company_address
            except:
                company_address = "null"
                data_dict['company_address'] = company_address

            print(data_dict)
            self.company_data.append(data_dict)
            self.export_data(self.company_data)

    # 学校地址抓取
    def college_reptile(self):
        school_list = ['浙江工商大学',
'浙江农林大学',
'浙江师范大学',
'浙江大学城市学院',
'浙江中医药大学中药饮片厂',
'浙江大学国家电网公司',
'浙江大学工业生态与环境研究所',
'浙江中医药大学',
'浙江大学宁波理工学院',
'浙江大学饲料科学研究所',
'浙江大学台州研究院',
'浙江大学苏州工业技术研究院直驱中心',
'浙江大学苏州工业技术研究院',
'浙江农林大学暨阳学院',
'浙江中医药大学附属第三医院',
'浙江树人大学',
'浙江大学滨海产业技术研究院',
'浙江大学常州工业技术研究院',
'浙江中医药大学附属第一医院',
'福建医科大学',
'福建师范大学福清分校',
'福建省电力有限公司华北电力大学',
'福建省电力有限公司电力科学研究院',
'福建医科大学附属口腔医院',
'福建中医药大学附属第三人民医院',
'江西财经大学',
'江西中医药大学',
'江西师范大学',
'江西理工大学应用科学学院',
'江西科技师范大学',
'江西农业大学',
'中国石油大学(华东)',
'中国矿业大学',
'中国农业大学',
'中国海洋大学',
'中国人民解放军海军工程大学',
'中国医药大学',
'中国人民解放军理工大学',
'中国传媒大学',
'中国人民解放军第三军医大学第三附属医院',
'中国人民解放军第三军医大学',
'中国人民解放军第三军医大学第一附属医院',
'中国人民解放军第三军医大学第二附属医院',
'中国人民解放军海军工程大学勤务学院',
'中国科学技术大学苏州研究院',
'中国人民大学',
'中国人民解放军第二炮兵工程大学',
'中国人民解放军陆军工程大学',
'中国人民解放军第四军医大学',
'中国人民解放军第三军医大学军事预防医学院',
'中国医科大学',
'中国人民公安大学',
'中国人民解放军国防科技大学',
'中国人民解放军陆军军医大学',
'中国人民解放军理工大学气象海洋学院',
'中国人民武装警察部队工程大学',
'中国人民解放军空军军医大学',
'中国人民解放军火箭军工程大学',
'中国人民解放军陆军军医大学',
'中国人民解放军空军航空大学',
'中国人民解放军空军工程大学',
'中国人民解放军战略支援部队信息工程大学',
'中国人民解放军海军航空大学',
'中国人民解放军海军军医大学',
'中国人民解放军陆军军医大学第二附属医院',
'中国人民解放军第二军医大学第二附属医院',
'中国地质大学武汉',
'中国人民解放军陆军军医大学第三附属医院(野战外科研究所)',
'中国人民解放军理工大学野战工程学院',
'中国人民解放军第二军医大学东方肝胆外科医院',
'南方医科大学基因工程研究所',
'南方医科大学',
'江南大学',
'东华大学',
'南方医科大学南方医院',
'华中师范大学',
'华中农业大学',
'南开大学',
'中南林业科技大学',
'中央大学',
'华东师范大学',
'中央民族大学',
'中原大学',
'华东交通大学',
'东海大学',
'江原大学校',
'中南民族大学',
'电子科技大学',
'广东电子信息工程研究院',
'南开大学生命科学学院',
'华北电力大学苏州研究院',
'华北理工大学',
'曲阜师范大学',
'电子科技大学中山学院',
'中州大学',
'曲阜师范大学杏坛学院',
'庆北大学校',
]
        for i in school_list:
            self.bro.get(
                "https://zh.wikipedia.org/w/index.php?search=%E5%9B%BD%E7%AB%8B%E5%A4%A7%E5%AD%A6%E6%B3%95%E4%BA%BA%E5%B1%B1%E5%8F%A3%E5%A4%A7%E5%AD%A6&title=Special%3A%E6%90%9C%E7%B4%A2&ns0=1")
            school_dict = dict()
            school_dict['search'] = i
            sleep(6)
            while True:
                try:
                    search_input_ex = self.bro.find_element(By.XPATH,
                                                            "//input[@name='search' and @placeholder='搜索维基百科' and @class='cdx-text-input__input']")
                    search_input_ex.clear()
                    search_input_ex.send_keys(i)
                    sleep(1)
                    break
                except:
                    continue

            search_button_ex = self.bro.find_element(By.XPATH,
                                                     "//button[text()='搜索' and contains(@class,'cdx-search-input__end-button')]")
            search_button_ex.click()

            sleep(1)

            try:
                search_results_ex = self.bro.find_element(By.XPATH,
                                                          "//ul[@class='mw-search-results']/li[1]//div[@class='mw-search-result-heading']/a")
                search_results_ex.click()
                sleep(5)
            except:
                print("暂无搜索结果")

            try:
                school_title_ex = self.bro.find_element(By.XPATH,
                                                        "//table[@class='infobox vcard']//th[@class='fn org']")
                school_title = school_title_ex.text
                school_dict['school_title'] = school_title
            except:
                school_title = 'null'
                school_dict['school_title'] = school_title

            try:
                school_address_ex = self.bro.find_element(By.XPATH,
                                                          "//table[@class='infobox vcard']//th[text()='校址']/following-sibling::td[@class='adr']")
                school_address = school_address_ex.text
                school_dict['school_address'] = school_address
            except:
                school_address = 'null'
                school_dict['school_address'] = school_address

            print(school_dict)
            self.school_data.append(school_dict)

        self.export_data(self.school_data)

    # 导出为Excel文件
    def export_excel(self, filename):
        filePath = os.path.join(self.BASE_DIR, 'utils\\data.pickle')
        if os.path.exists(filePath):
            pass
        else:
            return "数据文件不存在，请先爬取数据后在导出数据"

        data = pickle.load(open(filePath, "rb"))
        data1 = []
        for i in data:
            if isinstance(i, dict):
                data1.append(i)

        df = pd.DataFrame(data=data1)
        filename = filename + '.xlsx'
        filePath = os.path.join(self.BASE_DIR, filename)
        try:
            df.to_excel(filePath, index=False)
            return "导出路径：" + filePath
        except Exception as e:
            return str(e)

    def export_data(self, data):
        filePath1 = os.path.join(self.BASE_DIR, 'utils\\data.pickle')
        with open(filePath1, 'wb') as f:
            pickle.dump(data, f)


if __name__ == '__main__':
    aqc = aiqicha()
    try:
        aqc.company_reptile()
    except:
        aqc.export_data(aqc.company_data)
    aqc.export_excel("公司_缺失")
