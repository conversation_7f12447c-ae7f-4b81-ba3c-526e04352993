#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
闲鱼接口认证信息获取帮助工具
"""

import json
import base64
from datetime import datetime

def decode_jwt_token(token):
    """解码JWT token"""
    try:
        # JWT token由三部分组成，用.分隔
        parts = token.split('.')
        if len(parts) != 3:
            return None, "无效的JWT token格式"
        
        # 解码header
        header_data = parts[0]
        # 添加padding如果需要
        header_data += '=' * (4 - len(header_data) % 4)
        header = json.loads(base64.urlsafe_b64decode(header_data))
        
        # 解码payload
        payload_data = parts[1]
        # 添加padding如果需要
        payload_data += '=' * (4 - len(payload_data) % 4)
        payload = json.loads(base64.urlsafe_b64decode(payload_data))
        
        return {
            "header": header,
            "payload": payload,
            "signature": parts[2]
        }, None
        
    except Exception as e:
        return None, f"解码失败: {str(e)}"

def analyze_token(token):
    """分析token信息"""
    print("=" * 60)
    print("JWT Token 分析")
    print("=" * 60)
    
    decoded, error = decode_jwt_token(token)
    if error:
        print(f"❌ {error}")
        return False
    
    print("📋 Token 信息:")
    print(f"算法: {decoded['header'].get('alg', 'unknown')}")
    print(f"类型: {decoded['header'].get('typ', 'unknown')}")
    
    payload = decoded['payload']
    print(f"\n👤 用户信息:")
    print(f"用户ID: {payload.get('uid', 'unknown')}")
    print(f"会话ID: {payload.get('sid', 'unknown')}")
    print(f"用户名: {payload.get('name', 'unknown')}")
    print(f"类型: {payload.get('type', 'unknown')}")
    
    # 检查时间戳
    if 'iat' in payload:
        iat = datetime.fromtimestamp(payload['iat'])
        print(f"签发时间: {iat.strftime('%Y-%m-%d %H:%M:%S')}")
    
    if 'exp' in payload:
        exp = datetime.fromtimestamp(payload['exp'])
        now = datetime.now()
        print(f"过期时间: {exp.strftime('%Y-%m-%d %H:%M:%S')}")
        
        if exp > now:
            remaining = exp - now
            print(f"✅ Token有效，剩余时间: {remaining}")
        else:
            print(f"❌ Token已过期")
            return False
    
    return True

def get_auth_instructions():
    """获取认证信息的说明"""
    print("\n" + "=" * 60)
    print("如何获取完整的认证信息")
    print("=" * 60)
    
    print("""
🔧 方法1: 使用浏览器开发者工具

1. 打开Chrome浏览器，访问 https://goofish.pro
2. 登录您的闲鱼账号
3. 按F12打开开发者工具
4. 切换到 Network (网络) 标签
5. 在页面中执行订单查询操作
6. 在Network中找到对应的API请求
7. 右键点击请求 -> Copy -> Copy as cURL
8. 从cURL中提取以下信息：
   - Authorization header
   - Cookie header
   - 其他必要的header

🔧 方法2: 手动提取关键信息

需要的关键信息：
1. Authorization token (您已提供)
2. Cookie 信息 (包含session信息)
3. x-content-security 参数
4. 其他安全验证参数

📝 当前问题分析：

接口1错误: "安全验证失败-2"
- 可能缺少完整的cookie信息
- 可能需要更新x-content-security参数
- 可能需要特定的请求头组合

接口2错误: "Session过期"
- 需要有效的session cookie
- 可能需要重新登录获取新的session

🎯 建议步骤：

1. 先确保在浏览器中能正常访问闲鱼订单页面
2. 使用开发者工具抓取完整的请求信息
3. 更新脚本中的所有认证参数
4. 重新测试接口
""")

def main():
    """主函数"""
    # 您提供的token
    token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTQ0ODE3OTYsImlhdCI6MTc1Mzg3Njk5NiwibmFtZSI6Ikx1Y2tlcl9ZYW4iLCJzaWQiOjY5NzQzNDUwMjg0MDM4OSwidHlwZSI6MSwidWlkIjo2OTc0MzQ1MDI4NDAzOTB9.lJed8-xhejWvCBzcfv6pqIhtMx4UCIUNzJdR2DL_BWU"
    
    print("🔍 闲鱼接口认证分析工具")
    
    # 分析token
    token_valid = analyze_token(token)
    
    # 显示获取认证信息的说明
    get_auth_instructions()
    
    print("\n" + "=" * 60)
    print("总结")
    print("=" * 60)
    
    if token_valid:
        print("✅ 您的Authorization token是有效的")
        print("❌ 但是接口调用失败，可能需要以下信息：")
        print("   1. 完整的Cookie信息")
        print("   2. 正确的x-content-security参数")
        print("   3. 其他必要的请求头")
    else:
        print("❌ 您的Authorization token可能已过期或无效")
        print("   需要重新获取新的token")
    
    print("\n💡 下一步建议：")
    print("1. 使用浏览器开发者工具获取完整的请求信息")
    print("2. 更新脚本中的认证参数")
    print("3. 重新测试接口")

if __name__ == "__main__":
    main()
